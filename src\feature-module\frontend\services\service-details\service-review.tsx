import { useState, useEffect } from "react"
import { ReviewList } from "./components/review-list.tsx"
import { <PERSON>, CardB<PERSON>, Card<PERSON>eader, <PERSON><PERSON>, <PERSON>ner, Select, SelectItem, Chip } from "@heroui/react"
import {
  getReviewsByServiceId,
  Review as BackendReview,
  addReplyToReview,
  getRepliesForReview,
  updateReply,
  deleteReply,
  CreateReplyData,
  UpdateReplyData
} from "../../../../service/reviewService"
import { toast } from 'react-toastify'
import { Filter, SortAsc, SortDesc } from "lucide-react"
import { getUserDisplayName } from "../../../../utils/userUtils"

// Local interfaces for the component (mapped from backend Review interface)
export interface Review {
  id: string
  customerName: string
  customerAvatar?: string
  rating: number
  title: string
  content: string
  date: string
  replies: Reply[]
  // Reviewer information with fallback support
  reviewerInfo?: {
    name?: string
    email?: string
  }
  userName?: string
  name?: string
  userEmail?: string
  email?: string
  // Detailed ratings
  serviceRating?: number
  qualityRating?: number
  valueRating?: number
  communicationRating?: number
  timelinessRating?: number
  // Image data
  imageNames?: string[]
  imageUrls?: string[]
  // Like/reaction system
  likes: number
  dislikes: number
  userReaction?: 'like' | 'dislike' | null
  // Additional metadata
  isVerified?: boolean
  helpfulCount?: number
}

export interface Reply {
  id: string
  authorName: string
  authorAvatar?: string
  content: string
  date: string
  isBusinessOwner?: boolean
  // Nested replies
  replies?: Reply[]
  // Like system for replies
  likes: number
  dislikes: number
  userReaction?: 'like' | 'dislike' | null
}

// Pagination interface
interface PaginationState {
  currentPage: number
  totalPages: number
  totalReviews: number
  reviewsPerPage: number
}

// Filter and sort interfaces
interface FilterState {
  rating: string // 'all' | '1' | '2' | '3' | '4'
  sortBy: string // 'newest' | 'oldest' | 'highest' | 'lowest'
}

// Props interface for the component
interface ReviewServiceProps {
  serviceId: string
  providerId?: string
  onAddReview?: () => void
}

export default function ReviewService({ serviceId, onAddReview }: ReviewServiceProps) {
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalReviews: 0,
    reviewsPerPage: 10
  })

  // Filter and sort state
  const [filters, setFilters] = useState<FilterState>({
    rating: 'all',
    sortBy: 'newest'
  })
  const [showFilters, setShowFilters] = useState(false)

  // Filter and sort reviews locally
  const filterAndSortReviews = (reviewsToFilter: Review[]): Review[] => {
    let filtered = [...reviewsToFilter]

    // Filter by rating
    if (filters.rating !== 'all') {
      const targetRating = parseInt(filters.rating)
      filtered = filtered.filter(review => review.rating === targetRating)
    }

    // Sort reviews
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'newest':
          return new Date(b.date).getTime() - new Date(a.date).getTime()
        case 'oldest':
          return new Date(a.date).getTime() - new Date(b.date).getTime()
        case 'highest':
          return b.rating - a.rating
        case 'lowest':
          return a.rating - b.rating
        default:
          return 0
      }
    })

    return filtered
  }

  // Load reviews from backend
  const loadReviews = async (page: number = 1) => {
    if (!serviceId) {
      setError('Service ID is required')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await getReviewsByServiceId(serviceId, {
        page,
        limit: pagination.reviewsPerPage
      })

      const mappedReviews = response.reviews.map((backendReview: BackendReview): Review => ({
        id: backendReview._id || backendReview.id || 'unknown',
        customerName: getUserDisplayName(backendReview.userName, backendReview.name, backendReview.userEmail),
        customerAvatar: backendReview.userProfileImage || backendReview.profileImage,
        rating: backendReview.rating || 0,
        title: backendReview.title || 'No title',
        content: backendReview.comment || backendReview.review || 'No content',
        date: new Date(backendReview.createdAt || backendReview.date || Date.now()).toLocaleDateString(),
        replies: [], // Backend reviews don't have replies yet
        // Detailed ratings
        serviceRating: backendReview.serviceRating,
        qualityRating: backendReview.qualityRating,
        valueRating: backendReview.valueRating,
        communicationRating: backendReview.communicationRating,
        timelinessRating: backendReview.timelinessRating,
        // Image data
        imageNames: backendReview.imageNames || [],
        imageUrls: backendReview.imageUrls || [],
        // Like/reaction system (default values for now)
        likes: 0,
        dislikes: 0,
        userReaction: null,
        // Additional metadata
        isVerified: backendReview.isVerified || false,
        helpfulCount: 0
      }))

      // Apply filtering and sorting
      const filteredReviews = filterAndSortReviews(mappedReviews)
      setReviews(filteredReviews)

      setPagination({
        currentPage: response.page,
        totalPages: response.totalPages,
        totalReviews: response.total,
        reviewsPerPage: response.limit
      })

      if (mappedReviews.length === 0 && page === 1) {
        // No reviews found for this service
        console.log('No reviews found for service:', serviceId)
      }
    } catch (error) {
      console.error('Error loading reviews:', error)
      setError('Failed to load reviews. Please try again later.')
      toast.error('Failed to load reviews')
    } finally {
      setLoading(false)
    }
  }

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setPagination(prev => ({ ...prev, currentPage: 1 })) // Reset to first page
  }



  useEffect(() => {
    loadReviews(1)
  }, [serviceId]) // eslint-disable-line react-hooks/exhaustive-deps

  // Reload reviews when filters change
  useEffect(() => {
    if (serviceId) {
      loadReviews(1)
    }
  }, [filters]) // eslint-disable-line react-hooks/exhaustive-deps

  // Handle pagination
  const handlePageChange = (page: number) => {
    loadReviews(page)
  }



  const handleNewReply = async (reviewId: string, reply: Omit<Reply, "id">) => {
    try {
      const replyData: CreateReplyData = {
        authorName: reply.authorName,
        content: reply.content,
        isBusinessOwner: reply.isBusinessOwner
      }

      const newReply = await addReplyToReview(reviewId, replyData)

      // Map backend reply to frontend Reply interface
      const mappedReply: Reply = {
        id: newReply._id || newReply.id || Date.now().toString(),
        authorName: newReply.authorName,
        authorAvatar: reply.authorAvatar,
        content: newReply.content,
        date: new Date(newReply.createdAt || Date.now()).toLocaleDateString(),
        isBusinessOwner: newReply.isBusinessOwner,
        replies: [],
        likes: newReply.likes || 0,
        dislikes: newReply.dislikes || 0,
        userReaction: newReply.userReaction || null
      }

      setReviews(
        reviews.map((review) =>
          review.id === reviewId ? { ...review, replies: [...review.replies, mappedReply] } : review,
        ),
      )

      toast.success('Reply added successfully!')
    } catch (error) {
      console.error('Error adding reply:', error)
      toast.error('Failed to add reply. Please try again.')
    }
  }

  // Handle updating a reply
  const handleUpdateReply = async (reviewId: string, replyId: string, content: string) => {
    try {
      const updateData: UpdateReplyData = { content }
      const updatedReply = await updateReply(reviewId, replyId, updateData)

      setReviews(
        reviews.map((review) => {
          if (review.id === reviewId) {
            const updatedReplies = review.replies.map((reply) =>
              reply.id === replyId
                ? {
                    ...reply,
                    content: updatedReply.content,
                    date: new Date(updatedReply.updatedAt || Date.now()).toLocaleDateString()
                  }
                : reply
            )
            return { ...review, replies: updatedReplies }
          }
          return review
        })
      )

      toast.success('Reply updated successfully!')
    } catch (error) {
      console.error('Error updating reply:', error)
      toast.error('Failed to update reply. Please try again.')
    }
  }

  // Handle deleting a reply
  const handleDeleteReply = async (reviewId: string, replyId: string) => {
    try {
      await deleteReply(reviewId, replyId)

      setReviews(
        reviews.map((review) => {
          if (review.id === reviewId) {
            const filteredReplies = review.replies.filter((reply) => reply.id !== replyId)
            return { ...review, replies: filteredReplies }
          }
          return review
        })
      )

      toast.success('Reply deleted successfully!')
    } catch (error) {
      console.error('Error deleting reply:', error)
      toast.error('Failed to delete reply. Please try again.')
    }
  }

  // Load replies for a specific review
  const loadRepliesForReview = async (reviewId: string) => {
    try {
      const repliesResponse = await getRepliesForReview(reviewId)

      const mappedReplies: Reply[] = repliesResponse.replies.map((backendReply) => ({
        id: backendReply._id || backendReply.id || 'unknown',
        authorName: backendReply.authorName,
        authorAvatar: undefined, // Backend doesn't provide avatar
        content: backendReply.content,
        date: new Date(backendReply.createdAt || Date.now()).toLocaleDateString(),
        isBusinessOwner: backendReply.isBusinessOwner,
        replies: [], // Nested replies not supported yet
        likes: backendReply.likes || 0,
        dislikes: backendReply.dislikes || 0,
        userReaction: backendReply.userReaction || null
      }))

      // Update the specific review with loaded replies
      setReviews(
        reviews.map((review) =>
          review.id === reviewId ? { ...review, replies: mappedReplies } : review
        )
      )
    } catch (error) {
      console.error('Error loading replies:', error)
      toast.error('Failed to load replies.')
    }
  }

  // Handle like/dislike for reviews
  const handleReviewLike = (reviewId: string, type: 'like' | 'dislike') => {
    setReviews((prevReviews) =>
      prevReviews.map((review) => {
        if (review.id === reviewId) {
          const currentReaction = review.userReaction
          let newLikes = review.likes
          let newDislikes = review.dislikes
          let newReaction: 'like' | 'dislike' | null = type

          // Handle previous reaction
          if (currentReaction === 'like') {
            newLikes -= 1
          } else if (currentReaction === 'dislike') {
            newDislikes -= 1
          }

          // Handle new reaction
          if (currentReaction === type) {
            // Same reaction - remove it
            newReaction = null
          } else {
            // Different or no previous reaction - add new one
            if (type === 'like') {
              newLikes += 1
            } else {
              newDislikes += 1
            }
          }

          return {
            ...review,
            likes: newLikes,
            dislikes: newDislikes,
            userReaction: newReaction
          }
        }
        return review
      })
    )

    // Here you would typically make an API call to persist the like/dislike
    toast.success(`Review ${type}d successfully!`)
  }

  // Handle like/dislike for replies
  const handleReplyLike = (reviewId: string, replyId: string, type: 'like' | 'dislike') => {
    setReviews((prevReviews) =>
      prevReviews.map((review) => {
        if (review.id === reviewId) {
          const updatedReplies = review.replies.map((reply) => {
            if (reply.id === replyId) {
              const currentReaction = reply.userReaction
              let newLikes = reply.likes
              let newDislikes = reply.dislikes
              let newReaction: 'like' | 'dislike' | null = type

              // Handle previous reaction
              if (currentReaction === 'like') {
                newLikes -= 1
              } else if (currentReaction === 'dislike') {
                newDislikes -= 1
              }

              // Handle new reaction
              if (currentReaction === type) {
                // Same reaction - remove it
                newReaction = null
              } else {
                // Different or no previous reaction - add new one
                if (type === 'like') {
                  newLikes += 1
                } else {
                  newDislikes += 1
                }
              }

              return {
                ...reply,
                likes: newLikes,
                dislikes: newDislikes,
                userReaction: newReaction
              }
            }
            return reply
          })

          return { ...review, replies: updatedReplies }
        }
        return review
      })
    )

    // Here you would typically make an API call to persist the like/dislike
    toast.success(`Reply ${type}d successfully!`)
  }

  const averageRating =
    reviews.length > 0 ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1) : "0.0"

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <Spinner size="lg" color="primary" />
          <p className="text-gray-500 mt-4">Loading reviews...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <p className="text-red-500 mb-4">{error}</p>
          <Button
            color="primary"
            variant="solid"
            onPress={() => loadReviews(pagination.currentPage)}
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Customer Reviews</h1>
            <p className="text-gray-600">Share your experience and read what others have to say</p>
          </div>
          {onAddReview && (
            <Button
              color="primary"
              variant="solid"
              onPress={onAddReview}
              startContent={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              }
            >
              Add Review
            </Button>
          )}
        </div>
      </div>

      {reviews.length > 0 ? (
        <>
          <div className="grid gap-6 mb-8">
            <Card>
              <CardHeader>
                <div>
                  <h3 className="text-lg font-semibold">Review Summary</h3>
                  <p className="text-sm text-gray-600">
                    {pagination.totalReviews} reviews • Average rating: {averageRating}/4.0
                  </p>
                </div>
              </CardHeader>
              <CardBody>
                <div className="flex items-center gap-4">
                  <div className="text-3xl font-bold">{averageRating}</div>
                  <div className="flex-1">
                    {[4, 3, 2, 1].map((rating) => {
                      const count = reviews.filter((r) => r.rating === rating).length
                      const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0
                      return (
                        <div key={rating} className="flex items-center gap-2 text-sm">
                          <span className="w-3">{rating}</span>
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                          <span className="w-8 text-gray-500">{count}</span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Filter and Sort Controls */}
          <div className="mb-6">
            <div className="flex flex-wrap items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <Button
                size="sm"
                variant={showFilters ? "solid" : "flat"}
                color="primary"
                onPress={() => setShowFilters(!showFilters)}
                startContent={<Filter className="w-4 h-4" />}
              >
                Filters
              </Button>

              {showFilters && (
                <>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Rating:</span>
                    <Select
                      size="sm"
                      placeholder="All ratings"
                      selectedKeys={[filters.rating]}
                      onSelectionChange={(keys) => {
                        const rating = Array.from(keys)[0] as string
                        handleFilterChange({ rating })
                      }}
                      className="w-32"
                    >
                      <SelectItem key="all" value="all">All</SelectItem>
                      <SelectItem key="4" value="4">4 Stars</SelectItem>
                      <SelectItem key="3" value="3">3 Stars</SelectItem>
                      <SelectItem key="2" value="2">2 Stars</SelectItem>
                      <SelectItem key="1" value="1">1 Star</SelectItem>
                    </Select>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Sort by:</span>
                    <Select
                      size="sm"
                      placeholder="Sort by"
                      selectedKeys={[filters.sortBy]}
                      onSelectionChange={(keys) => {
                        const sortBy = Array.from(keys)[0] as string
                        handleFilterChange({ sortBy })
                      }}
                      className="w-36"
                    >
                      <SelectItem key="newest" value="newest" startContent={<SortDesc className="w-4 h-4" />}>
                        Newest
                      </SelectItem>
                      <SelectItem key="oldest" value="oldest" startContent={<SortAsc className="w-4 h-4" />}>
                        Oldest
                      </SelectItem>
                      <SelectItem key="highest" value="highest" startContent={<SortDesc className="w-4 h-4" />}>
                        Highest Rating
                      </SelectItem>
                      <SelectItem key="lowest" value="lowest" startContent={<SortAsc className="w-4 h-4" />}>
                        Lowest Rating
                      </SelectItem>
                    </Select>
                  </div>

                  {(filters.rating !== 'all' || filters.sortBy !== 'newest') && (
                    <Button
                      size="sm"
                      variant="flat"
                      onPress={() => handleFilterChange({ rating: 'all', sortBy: 'newest' })}
                    >
                      Clear Filters
                    </Button>
                  )}
                </>
              )}
            </div>

            {/* Active Filter Chips */}
            {(filters.rating !== 'all' || filters.sortBy !== 'newest') && (
              <div className="flex flex-wrap gap-2 mt-2">
                {filters.rating !== 'all' && (
                  <Chip
                    size="sm"
                    variant="flat"
                    color="primary"
                    onClose={() => handleFilterChange({ rating: 'all' })}
                  >
                    {filters.rating} Star{filters.rating !== '1' ? 's' : ''}
                  </Chip>
                )}
                {filters.sortBy !== 'newest' && (
                  <Chip
                    size="sm"
                    variant="flat"
                    color="primary"
                    onClose={() => handleFilterChange({ sortBy: 'newest' })}
                  >
                    Sort: {filters.sortBy === 'oldest' ? 'Oldest' :
                           filters.sortBy === 'highest' ? 'Highest Rating' :
                           filters.sortBy === 'lowest' ? 'Lowest Rating' : 'Newest'}
                  </Chip>
                )}
              </div>
            )}
          </div>

          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">All Reviews</h2>
              {pagination.totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="text-sm text-gray-600">
                    Showing {((pagination.currentPage - 1) * pagination.reviewsPerPage) + 1} to{' '}
                    {Math.min(pagination.currentPage * pagination.reviewsPerPage, pagination.totalReviews)} of{' '}
                    {pagination.totalReviews} reviews
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="flat"
                      isDisabled={pagination.currentPage === 1}
                      onPress={() => handlePageChange(pagination.currentPage - 1)}
                      startContent={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                        </svg>
                      }
                    >
                      Previous
                    </Button>

                    {/* Page Numbers */}
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum: number
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1
                        } else if (pagination.currentPage <= 3) {
                          pageNum = i + 1
                        } else if (pagination.currentPage >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i
                        } else {
                          pageNum = pagination.currentPage - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            size="sm"
                            variant={pagination.currentPage === pageNum ? "solid" : "flat"}
                            color={pagination.currentPage === pageNum ? "primary" : "default"}
                            onPress={() => handlePageChange(pageNum)}
                            className="min-w-8 w-8 h-8"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      size="sm"
                      variant="flat"
                      isDisabled={pagination.currentPage === pagination.totalPages}
                      onPress={() => handlePageChange(pagination.currentPage + 1)}
                      endContent={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                      }
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
            <ReviewList
              reviews={reviews}
              onReply={handleNewReply}
              onLike={handleReviewLike}
              onReplyLike={handleReplyLike}
              onUpdateReply={handleUpdateReply}
              onDeleteReply={handleDeleteReply}
              onLoadReplies={loadRepliesForReview}
            />

            {/* Bottom Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="flat"
                    isDisabled={pagination.currentPage === 1}
                    onPress={() => handlePageChange(pagination.currentPage - 1)}
                    startContent={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                    }
                  >
                    Previous
                  </Button>

                  {/* Page Numbers */}
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      let pageNum: number
                      if (pagination.totalPages <= 5) {
                        pageNum = i + 1
                      } else if (pagination.currentPage <= 3) {
                        pageNum = i + 1
                      } else if (pagination.currentPage >= pagination.totalPages - 2) {
                        pageNum = pagination.totalPages - 4 + i
                      } else {
                        pageNum = pagination.currentPage - 2 + i
                      }

                      return (
                        <Button
                          key={pageNum}
                          size="sm"
                          variant={pagination.currentPage === pageNum ? "solid" : "flat"}
                          color={pagination.currentPage === pageNum ? "primary" : "default"}
                          onPress={() => handlePageChange(pageNum)}
                          className="min-w-8 w-8 h-8"
                        >
                          {pageNum}
                        </Button>
                      )
                    })}
                  </div>

                  <Button
                    size="sm"
                    variant="flat"
                    isDisabled={pagination.currentPage === pagination.totalPages}
                    onPress={() => handlePageChange(pagination.currentPage + 1)}
                    endContent={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                      </svg>
                    }
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <div className="mb-4">
            <svg
              className="w-16 h-16 mx-auto text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No reviews yet</h3>
          <p className="text-gray-500">Be the first to share your experience with this service!</p>
        </div>
      )}
    </div>
  )
}

